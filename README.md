# 海博平台API性能测试

这是一个使用 Locust 框架对海博平台API接口进行性能测试的项目，支持以下功能：
- 骑手位置查询接口性能测试
- 配送商门店创建接口性能测试

## 项目结构

```
stress/
├── config.py                      # 配置文件（API密钥、测试数据等）
├── haibo_api.py                   # 海博API客户端（签名生成、请求处理）
├── locustfile.py                  # 骑手位置查询性能测试用例
├── delivery_store_locustfile.py   # 配送商门店创建性能测试用例
├── test_haibo_api.py              # API测试脚本
├── pyproject.toml                 # 项目配置
└── README.md                     # 项目说明
```

## 功能特性

### 骑手位置查询
- ✅ 完整的海博API签名算法实现
- ✅ 支持订单号查询和订单号+门店ID查询两种场景
- ✅ 包含错误处理测试（无效订单号）
- ✅ 详细的性能指标收集
- ✅ 可配置的测试数据和参数

### 配送商门店创建
- ✅ 支持配送商门店创建接口性能测试
- ✅ 自动生成唯一门店ID避免重复创建
- ✅ 支持所有必填和可选参数
- ✅ 完整的错误处理和响应验证

## 安装依赖

```bash
# 使用 uv 安装依赖
uv sync

# 或者使用 pip
pip install locust requests
```

## 配置

在 `config.py` 中配置你的海博平台认证信息：

```python
HAIBO_DEVELOPER_ID = "your-developer-id"
HAIBO_SECRET = "your-secret"
```

同时可以配置测试用的订单号和门店ID：

```python
TEST_ORDER_IDS = [
    "your-test-order-id-1",
    "your-test-order-id-2",
    # ...
]

TEST_CARRIER_SHOP_IDS = [
    "your-shop-id-1",
    "your-shop-id-2",
    # ...
]

# 配送商门店创建测试数据
TEST_STORE_DATA = [
    {
        "shopId": "23412",
        "shopName": "测试门店001",
        "contactPhone": "18812345678",
        "shopAddress": "北京市朝阳区广顺北大街666号",
        "shopLng": 116398419,  # 火星坐标 * 10^6
        "shopLat": 39908722,   # 火星坐标 * 10^6
        "carrierMerchantId": "merchant001",
        "category": 1
    },
    # ...
]
```

## 使用方法

### 1. 验证API配置

首先运行测试脚本验证API配置是否正确：

```bash
python test_haibo_api.py
```

### 2. 运行性能测试

#### 骑手位置查询测试

**Web界面模式（推荐）**

```bash
locust -f locustfile.py --host=https://test.fengqishi.com.cn
```

然后打开浏览器访问 http://localhost:8089，在Web界面中配置：
- 用户数量（Number of users）
- 每秒启动用户数（Spawn rate）
- 测试时长

**命令行模式**

```bash
# 10个用户，每秒启动2个用户，运行60秒
locust -f locustfile.py --host=https://test.fengqishi.com.cn \
       --users 10 --spawn-rate 2 --run-time 60s --headless
```

#### 配送商门店创建测试

**Web界面模式（推荐）**

```bash
locust -f delivery_store_locustfile.py --host=https://test.fengqishi.com.cn
```

**命令行模式**

```bash
# 5个用户，每秒启动1个用户，运行60秒（门店创建频率较低）
locust -f delivery_store_locustfile.py --host=https://test.fengqishi.com.cn \
       --users 5 --spawn-rate 1 --run-time 60s --headless
```

## 测试场景

### 骑手位置查询测试场景

1. **使用订单号+门店ID查询**
   - 随机选择测试订单号和门店ID
   - 调用 `/api/haibo/rider-location` 接口
   - 验证响应格式和内容

### 配送商门店创建测试场景

1. **创建配送商门店**
   - 随机选择门店测试数据
   - 自动生成唯一的shopId（添加时间戳和随机后缀）
   - 调用 `/api/haibo/delivery-merchant-store` 接口
   - 验证创建结果

## 性能指标

测试会收集以下性能指标：

- **响应时间**：平均响应时间、95%分位数、99%分位数
- **吞吐量**：每秒请求数（RPS）
- **成功率**：请求成功率
- **错误率**：各种错误的分布

## API签名算法

项目实现了完整的海博平台API签名算法：

1. 过滤空值参数和sign参数
2. 按参数名字典顺序排序
3. 拼接参数：参数1值1参数2值2...
4. 加上secret前缀
5. SHA1加密并转为小写

## 注意事项

- 确保测试环境网络稳定
- 测试前请确认API配置正确
- 建议先进行小规模测试验证功能
- 生产环境测试需要提前与海博平台沟通