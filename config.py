"""
海博平台API配置文件
"""

# 海博平台配置
HAIBO_DEVELOPER_ID = "d8019ab5-4c89-43f1-ae8e-693bf29293b0"
HAIBO_SECRET = "f9141392-333b-44b2-8e00-2e0622578a91"

# API配置
API_VERSION = "1.0"
BASE_URL = "https://test.fengqishi.com.cn"

# 测试配置
TEST_ORDER_IDS = [
    "HB13586463575060480",
    "HB13567484013916160"
]

TEST_CARRIER_SHOP_IDS = [
    "shop001",
    "shop002",
    "shop003",
]

# 配送商门店创建测试数据
TEST_STORE_DATA = [
    {
        "shopId": "23412",
        "shopName": "测试门店001",
        "contactPhone": "18812345678",
        "shopAddress": "海创科技中心",
        "shopLng": 120000000,
        "shopLat": 30280000,
        "carrierMerchantId": "merchant001",
        "category": 1
    }
]
