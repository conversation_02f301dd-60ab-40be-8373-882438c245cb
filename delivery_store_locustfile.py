"""
海博平台配送商门店创建接口性能测试
"""

import random
import time
from locust import HttpUser, task, between,constant_throughput
from locust.exception import RescheduleTask

from haibo_api import HaiboAPI
from config import TEST_STORE_DATA


class HaiboDeliveryMerchantStoreUser(HttpUser):
    """海博平台配送商门店创建性能测试用户"""
    
    wait_time = constant_throughput(1)
    
    def on_start(self):
        """测试开始时的初始化"""
        self.api_client = HaiboAPI()
        print(f"配送商门店创建用户 {self.environment.runner.user_count} 开始测试")

    @task
    def create_delivery_merchant_store(self):
        """
        创建配送商门店
        """
        # 随机选择门店数据
        store_data = random.choice(TEST_STORE_DATA)
        
        # 为了避免重复创建，给shopId添加随机后缀和时间戳
        store_data_copy = store_data.copy()
        timestamp = int(time.time())
        random_suffix = random.randint(1000, 9999)
        store_data_copy['shopId'] = f"{store_data['shopId']}_{timestamp}_{random_suffix}"
        store_data_copy['shopName'] = f"{store_data['shopName']}_{random_suffix}"
        
        start_time = time.time()
        
        try:
            # 调用API
            response = self.api_client.create_delivery_merchant_store(
                shop_id=store_data_copy['shopId'],
                shop_name=store_data_copy['shopName'],
                contact_phone=store_data_copy['contactPhone'],
                shop_address=store_data_copy['shopAddress'],
                shop_lng=store_data_copy['shopLng'],
                shop_lat=store_data_copy['shopLat'],
                carrier_merchant_id=store_data_copy['carrierMerchantId'],
                category=store_data_copy.get('category')
            )
            
            # 计算响应时间
            response_time = int((time.time() - start_time) * 1000)
            
            # 验证响应
            if self.api_client.validate_response(response):
                # 记录成功请求
                self.environment.events.request.fire(
                    request_type="POST",
                    name="/api/haibo/store",
                    response_time=response_time,
                    response_length=len(response.content),
                    exception=None,
                    context={}
                )
                print(f"成功创建门店: {store_data_copy['shopId']}")
            else:
                # 记录失败请求
                self.environment.events.request.fire(
                    request_type="POST",
                    name="/api/haibo/store",
                    response_time=response_time,
                    response_length=len(response.content),
                    exception=Exception(f"API返回错误: {response.text}"),
                    context={}
                )
                print(f"创建门店失败: {store_data_copy['shopId']}, 错误: {response.text}")
                
        except Exception as e:
            # 记录异常
            response_time = int((time.time() - start_time) * 1000)
            self.environment.events.request.fire(
                request_type="POST",
                name="/api/haibo/store",
                response_time=response_time,
                response_length=0,
                exception=e,
                context={}
            )
            print(f"创建门店异常: {store_data_copy['shopId']}, 异常: {str(e)}")
