"""
海博平台API签名和请求处理
"""

import hashlib
import time
import urllib.parse
from typing import Dict, Any, Optional

import requests

from config import HAIBO_DEVELOPER_ID, HAIBO_SECRET, API_VERSION, BASE_URL


class HaiboAPI:
    """海博平台API客户端"""
    
    def __init__(self):
        self.developer_id = HAIBO_DEVELOPER_ID
        self.secret = HAIBO_SECRET
        self.version = API_VERSION
        self.base_url = BASE_URL
        
    def _generate_signature(self, params: Dict[str, Any]) -> str:
        """
        生成API签名

        签名规则：
        1. 将所有参数（除sign、byte[]及值为空的参数外）按参数名字典顺序排序
        2. 将参数以参数1值1参数2值2...的顺序拼接
        3. 按照secret + 排序后的参数的顺序进行连接
        4. 对加密前的字符串进行sha1加密并转为小写字符串
        """
        # 过滤空值参数和sign参数，注意：空字符串也要过滤掉
        filtered_params = {}
        for k, v in params.items():
            if k != 'sign' and v is not None and str(v) != '':
                filtered_params[k] = str(v)

        # 按参数名字典顺序排序
        sorted_params = sorted(filtered_params.items())

        # 拼接参数：参数名1值1参数名2值2...
        param_string = ''.join([f"{k}{v}" for k, v in sorted_params])

        # 加上secret前缀
        sign_string = self.secret + param_string

        # SHA1加密并转小写
        signature = hashlib.sha1(sign_string.encode('utf-8')).hexdigest().lower()

        return signature
    
    def _prepare_common_params(self) -> Dict[str, Any]:
        """准备公共参数"""
        timestamp = int(time.time())
        
        params = {
            'developerId': self.developer_id,
            'timestamp': timestamp,
            'version': self.version,
        }
        
        return params
    
    def get_rider_location(self, order_id: str, carrier_shop_id: Optional[str] = None) -> requests.Response:
        """
        查询骑手位置
        
        Args:
            order_id: 海博平台订单号
            carrier_shop_id: 配送商门店id（可选）
            
        Returns:
            requests.Response: HTTP响应对象
        """
        # 准备请求参数
        params = self._prepare_common_params()
        params['orderId'] = order_id
        
        if carrier_shop_id:
            params['carrierShopId'] = carrier_shop_id
            
        # 生成签名
        params['sign'] = self._generate_signature(params)
        
        # 发送请求
        url = f"{self.base_url}/api/haibo/rider-location"
        
        response = requests.post(
            url,
            data=params,
            headers={
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'HaiboAPI-StressTest/1.0'
            },
            timeout=30
        )
        
        return response
    
    def create_delivery_merchant_store(self, shop_id: str, shop_name: str, contact_phone: str,
                                     shop_address: str, shop_lng: int, shop_lat: int,
                                     carrier_merchant_id: str, category: Optional[int] = None) -> requests.Response:
        """
        创建配送商门店

        Args:
            shop_id: 海博发货门店id
            shop_name: 海博发货门店名称
            contact_phone: 门店联系人电话
            shop_address: 门店详细地址
            shop_lng: 门店经度（火星坐标，坐标 * 10^6）
            shop_lat: 门店纬度（火星坐标，坐标 * 10^6）
            carrier_merchant_id: 配送商商户id
            category: 商家经营的品类信息（可选）

        Returns:
            requests.Response: HTTP响应对象
        """
        # 准备请求参数
        params = self._prepare_common_params()
        params.update({
            'shopId': shop_id,
            'shopName': shop_name,
            'contactPhone': contact_phone,
            'shopAddress': shop_address,
            'shopLng': shop_lng,
            'shopLat': shop_lat,
            'carrierMerchantId': carrier_merchant_id
        })

        if category is not None:
            params['category'] = category

        # 生成签名
        params['sign'] = self._generate_signature(params)

        # 发送请求
        url = f"{self.base_url}/api/haibo/store"

        response = requests.post(
            url,
            data=params,
            headers={
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'HaiboAPI-StressTest/1.0'
            },
            timeout=30
        )


        return response

    def validate_response(self, response: requests.Response) -> bool:
        """
        验证响应是否成功

        Args:
            response: HTTP响应对象

        Returns:
            bool: 是否成功
        """
        try:
            if response.status_code != 200:
                return False

            data = response.json()
            print(data)
            return data.get('code') == 0

        except Exception:
            return False
